﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Timers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using QuantConnect.Brokerages.Mexc.Messages;
using QuantConnect.Configuration;
using QuantConnect.Data;
using QuantConnect.Data.Market;
using QuantConnect.Interfaces;
using QuantConnect.Logging;
using QuantConnect.Orders;
using QuantConnect.Orders.Fees;
using QuantConnect.Packets;
using QuantConnect.Securities;
using QuantConnect.Util;
using Timer = System.Timers.Timer;

namespace QuantConnect.Brokerages.Mexc {
  [BrokerageFactory(typeof(MexcBrokerageFactory))]
  public class MexcBrokerage: BaseWebsocketsBrokerage
    , IDataQueueHandler {
    private IAlgorithm _algorithm;
    private SymbolPropertiesDatabaseSymbolMapper _symbolMapper;
    private readonly RateGate _webSocketRateLimiter = new RateGate(1, TimeSpan.FromMilliseconds(500));
    private LiveNodePacket _job;
    private string _webSocketBaseUrl;
    private Timer _keepAliveTimer;
    private IDataAggregator _aggregator;
    private BrokerageConcurrentMessageHandler<WebSocketMessage> _messageHandler;
    protected readonly object TickLocker = new object();
    protected string MarketName { get; set; }
    private MexcRestApiClient _restApiClient;
    private string _apiKey;
    private string _apiSecret;
    private bool _isAuthenticated;

    public MexcBrokerage()
      : this("mexc") {
    }

    public MexcBrokerage(string marketName)
      : base(marketName) {
      MarketName = marketName;
    }
    public override bool IsConnected => WebSocket?.IsOpen == true;

#region IDataQueueHandler

    public IEnumerator<BaseData> Subscribe(SubscriptionDataConfig dataConfig, EventHandler newDataAvailableHandler) {
      if (!CanSubscribe(dataConfig.Symbol)) {
        return null;
      }
      var enumerator = _aggregator.Add(dataConfig, newDataAvailableHandler);
      SubscriptionManager.Subscribe(dataConfig);
      return enumerator;
    }

    public void Unsubscribe(SubscriptionDataConfig dataConfig) {
      SubscriptionManager.Unsubscribe(dataConfig);
      _aggregator.Remove(dataConfig);
    }

    public void SetJob(LiveNodePacket job) {
      var aggregator = Composer.Instance.GetExportedValueByTypeName<IDataAggregator>(
        Config.Get("data-aggregator", "QuantConnect.Lean.Engine.DataFeeds.AggregationManager"), forceTypeNameOnExisting: false);

      SetJobInit(job, aggregator);

      if (!IsConnected) {
        Connect();
      }
    }

    private void SetJobInit(LiveNodePacket job, IDataAggregator aggregator) {
      _job = job;
      _aggregator = aggregator;
      _webSocketBaseUrl = "wss://contract.mexc.com/edge";
      _symbolMapper = new SymbolPropertiesDatabaseSymbolMapper(MarketName);

      _apiKey = Config.Get("mexc-api-key", "");
      _apiSecret = Config.Get("mexc-api-secret", "");
      var restApiUrl = Config.Get("mexc-rest-api-url", "https://contract.mexc.com");
      var restRateLimiter = new RateGate(20, TimeSpan.FromSeconds(2));

      _restApiClient = new MexcRestApiClient(_symbolMapper, null, _apiKey, _apiSecret, restApiUrl, restRateLimiter);

      Initialize(_webSocketBaseUrl, new WebSocketClientWrapper(), null, null, null);
      _messageHandler = new BrokerageConcurrentMessageHandler<WebSocketMessage>(OnDataMessage);

      var subscriptionManager = new EventBasedDataQueueHandlerSubscriptionManager();
      subscriptionManager.SubscribeImpl += (symbols, tickType) => Subscribe(symbols);
      subscriptionManager.UnsubscribeImpl += (symbols, tickType) => Unsubscribe(symbols);
      SubscriptionManager = subscriptionManager;
    }

#endregion
#region Brokerage

    public override List<Order> GetOpenOrders() {
      return new List<Order>();
    }

    public override List<Holding> GetAccountHoldings() {
      return new List<Holding>();
    }

    public override List<CashAmount> GetCashBalance() {
      if (_restApiClient == null) {
        return new List<CashAmount>();
      }

      try {
        var assets = _restApiClient.GetAccountAssets();
        var cashAmounts = new List<CashAmount>();

        foreach (var asset in assets) {
          if (asset.AvailableBalance > 0 || asset.FrozenBalance > 0) {
            var totalBalance = asset.AvailableBalance + asset.FrozenBalance;
            cashAmounts.Add(new CashAmount(totalBalance, asset.Currency));
          }
        }

        return cashAmounts;
      } catch (Exception ex) {
        Log.Error($"MexcBrokerage.GetCashBalance(): Error retrieving cash balance: {ex.Message}");
        Log.Error($"MexcBrokerage.GetCashBalance(): Stack trace: {ex.StackTrace}");
        return new List<CashAmount>();
      }
    }

    public override bool PlaceOrder(Order order) {
      return false;
    }

    public override bool UpdateOrder(Order order) {
      return false;
    }

    public override bool CancelOrder(Order order) {
      return false;
    }

    public override void Connect() {
      if (IsConnected)
        return;

      if (!IsInitialized) {
        Log.Trace("MexcBrokerage.Connect(): Not initialized, skipping connection...");
        return;
      }

      Log.Trace("MexcBrokerage.Connect(): Connecting...");
      base.Connect();
      StartKeepAliveTimer();

      if (!string.IsNullOrEmpty(_apiKey) && !string.IsNullOrEmpty(_apiSecret)) {
        AuthenticateWebSocket();
      }
    }

    public override void Disconnect() {
      Log.Trace("MexcBrokerage.Disconnect(): Disconnecting...");
      _keepAliveTimer?.Stop();
      WebSocket?.Close();
    }

#endregion
    protected override void OnMessage(object sender, WebSocketMessage e) {
      _messageHandler.HandleNewMessage(e);
    }

    private void OnDataMessage(WebSocketMessage webSocketMessage) {
      var e = (WebSocketClientWrapper.TextMessage)webSocketMessage.Data;
      try {
        var obj = JObject.Parse(e.Message);

        if (obj["channel"]?.ToString() == "pong") {
          return;
        }

        if (obj["channel"]?.ToString() == "push.deal") {
          var tradeMessage = obj.ToObject<MexcTradeMessage>();
          if (tradeMessage?.Data != null) {
            var brokerageSymbol = tradeMessage.Symbol.Replace("_USDT", "USDT");
            EmitTradeTick(
              _symbolMapper.GetLeanSymbol(brokerageSymbol, GetSupportedSecurityType(), MarketName),
              Time.UnixMillisecondTimeStampToDateTime(tradeMessage.Data.Timestamp),
              tradeMessage.Data.Price,
              tradeMessage.Data.Volume);
          }
        } else if (obj["channel"]?.ToString() == "push.personal.order.deal") {
          var orderDealMessage = obj.ToObject<MexcOrderDealMessage>();
          if (orderDealMessage?.Data != null) {
            OnFillOrder(orderDealMessage.Data);
          }
        } else if (obj["channel"]?.ToString() == "rs.login") {
          var loginResult = obj["data"]?.ToObject<bool>() ?? false;
          _isAuthenticated = loginResult;
          Log.Trace($"MexcBrokerage.OnDataMessage(): Login result: {loginResult}");
          if (loginResult) {
            SubscribeToPersonalOrderEvents();
          }
        } else {
          Log.Trace($"MexcBrokerage.OnDataMessage(): Unknown channel: {obj["channel"]}");
        }
      } catch (Exception exception) {
        OnMessage(new BrokerageMessageEvent(BrokerageMessageType.Error, -1, $"Parsing wss message failed. Data: {e.Message} Exception: {exception}"));
        Log.Error(exception);
      }
    }

    private void EmitTradeTick(Symbol symbol, DateTime time, decimal price, decimal quantity) {
      var tick = new Tick {
        Symbol = symbol,
        Value = price,
        Quantity = Math.Abs(quantity),
        Time = time,
        TickType = TickType.Trade
      };

      lock (TickLocker) {
        _aggregator.Update(tick);
      }
    }

    private void OnFillOrder(MexcOrderDeal data) {
      try {
        var order = _algorithm.Transactions.GetOrdersByBrokerageId(data.OrderId)?.SingleOrDefault();
        if (order == null) {
          Log.Error($"MexcBrokerage.OnFillOrder(): order not found: {data.OrderId}");
          return;
        }

        var fillPrice = data.Price;
        var fillQuantity = order.Direction == OrderDirection.Sell ? -data.Volume : data.Volume;
        var updTime = Time.UnixMillisecondTimeStampToDateTime(data.Timestamp);
        var orderFee = OrderFee.Zero;
        if (!string.IsNullOrEmpty(data.FeeCurrency) && data.Fee > 0) {
          orderFee = new OrderFee(new CashAmount(data.Fee, data.FeeCurrency));
        }
        var status = OrderStatus.Filled;
        var orderEvent = new OrderEvent(
          order.Id, order.Symbol, updTime, status, order.Direction, fillPrice, fillQuantity, orderFee, $"Mexc Order Event {order.Direction}");

        OnOrderEvent(orderEvent);
      } catch (Exception e) {
        Log.Error(e);
        throw;
      }
    }

    protected virtual SecurityType GetSupportedSecurityType() {
      return SecurityType.CryptoFuture;
    }

    private bool CanSubscribe(Symbol symbol) {
      if (symbol.Value.IndexOfInvariant("universe", true) != -1 || symbol.IsCanonical()) {
        return false;
      }
      return symbol.SecurityType == GetSupportedSecurityType();
    }

    protected override bool Subscribe(IEnumerable<Symbol> symbols) {
      Log.Trace($"MexcBrokerage.Subscribe(): Subscribing to {symbols.Count()} symbols");
      foreach (var symbol in symbols) {
        var brokerageSymbol = _symbolMapper.GetBrokerageSymbol(symbol);
        brokerageSymbol = brokerageSymbol.Replace("USDT", "_USDT");
        Log.Trace($"MexcBrokerage.Subscribe(): Subscribing to symbol {symbol} -> {brokerageSymbol}");
        var subscriptionMessage = new MexcSubscriptionMessage {
          Method = "sub.deal",
          Param = new MexcSubscriptionParam { Symbol = brokerageSymbol }
        };

        Send(subscriptionMessage);
      }
      return true;
    }

    private bool Unsubscribe(IEnumerable<Symbol> symbols) {
      foreach (var symbol in symbols) {
        var brokerageSymbol = _symbolMapper.GetBrokerageSymbol(symbol);
        brokerageSymbol = brokerageSymbol.Replace("USDT", "_USDT");
        var unsubscriptionMessage = new MexcSubscriptionMessage {
          Method = "unsub.deal",
          Param = new MexcSubscriptionParam { Symbol = brokerageSymbol }
        };

        Send(unsubscriptionMessage);
      }
      return true;
    }

    private void Send(object obj) {
      var json = JsonConvert.SerializeObject(obj);
      _webSocketRateLimiter.WaitToProceed();

      if (Log.DebuggingEnabled) {
        Log.Debug("MexcBrokerage.Send(): " + json);
      }
      WebSocket.Send(json);
    }

    private void StartKeepAliveTimer() {
      // https://mexcdevelop.github.io/apidocs/contract_v1_en/#websocket-api
      // If no ping is received within 1 minute, the connection will be disconnected. It is recommended to send a ping for 10-20 seconds
      _keepAliveTimer = new Timer(15000);
      _keepAliveTimer.Elapsed += (sender, e) =>
      {
        if (IsConnected) {
          Send(new MexcPingMessage());
        }
      };
      _keepAliveTimer.Start();
    }

    private void AuthenticateWebSocket() {
      try {
        var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString(CultureInfo.InvariantCulture);
        var signature = CreateSignature(_apiKey + timestamp, _apiSecret);

        var loginMessage = new MexcLoginMessage {
          Param = new MexcLoginParam {
            ApiKey = _apiKey,
            ReqTime = timestamp,
            Signature = signature
          }
        };

        Send(loginMessage);
        Log.Trace("MexcBrokerage.AuthenticateWebSocket(): Login message sent");
      } catch (Exception ex) {
        Log.Error($"MexcBrokerage.AuthenticateWebSocket(): Error during authentication: {ex.Message}");
      }
    }

    private void SubscribeToPersonalOrderEvents() {
      try {
        var subscriptionMessage = new MexcSubscriptionMessage {
          Method = "sub.personal.order.deal",
          Param = new { }
        };

        Send(subscriptionMessage);
        Log.Trace("MexcBrokerage.SubscribeToPersonalOrderEvents(): Subscribed to personal order deal events");
      } catch (Exception ex) {
        Log.Error($"MexcBrokerage.SubscribeToPersonalOrderEvents(): Error subscribing to personal order events: {ex.Message}");
      }
    }

    private string CreateSignature(string message, string secret) {
      using (var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secret))) {
        var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(message));
        return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
      }
    }

    public override IEnumerable<BaseData> GetHistory(Data.HistoryRequest request) {
      return Enumerable.Empty<BaseData>();
    }
  }
}